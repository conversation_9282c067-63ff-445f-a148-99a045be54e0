use gneurshk_parser::{Operator, Stmt};
use inkwell::IntPredicate;
use inkwell::builder::<PERSON>uild<PERSON>;
use inkwell::context::Context;
use inkwell::module::Module;
use inkwell::types::BasicMetadataTypeEnum;
use inkwell::values::{BasicValueEnum, FunctionValue, PointerValue};
use std::collections::HashMap;

pub struct Codegen<'ctx> {
    context: &'ctx Context,
    module: Module<'ctx>,
    builder: Builder<'ctx>,
    variables: HashMap<String, PointerValue<'ctx>>,
    functions: HashMap<String, FunctionValue<'ctx>>,
    current_function: Option<FunctionValue<'ctx>>,
}

impl<'ctx> Codegen<'ctx> {
    pub fn new(context: &'ctx Context, module_name: &str) -> Self {
        Self {
            context,
            module: context.create_module(module_name),
            builder: context.create_builder(),
            variables: HashMap::new(),
            functions: HashMap::new(),
            current_function: None,
        }
    }

    pub fn compile(&mut self, ast: Vec<Stmt>) -> &Module<'ctx> {
        // Create main function if it doesn't exist
        let main_type = self.context.i32_type().fn_type(&[], false);
        let main_function = self.module.add_function("main", main_type, None);
        let basic_block = self.context.append_basic_block(main_function, "entry");
        self.builder.position_at_end(basic_block);
        self.current_function = Some(main_function);

        // Compile all statements
        for stmt in ast {
            self.compile_stmt(stmt);
        }

        // Return 0 from main if no explicit return
        if let Some(current_fn) = self.current_function {
            if current_fn.get_name().to_str().unwrap() == "main" {
                let zero = self.context.i32_type().const_int(0, false);
                self.builder.build_return(Some(&zero)).unwrap();
            }
        }

        &self.module
    }

    fn compile_stmt(&mut self, stmt: Stmt) -> Option<BasicValueEnum<'ctx>> {
        match stmt {
            Stmt::Declaration {
                mutable: _,
                name,
                value,
            } => self.compile_declaration(name, value),
            Stmt::Block { body } => self.compile_block(body),
            Stmt::IfStatement { condition, block } => self.compile_if_statement(*condition, *block),
            Stmt::FunctionDeclaration {
                name,
                params,
                return_type,
                block,
            } => self.compile_function_declaration(name, params, return_type, *block),
            Stmt::FunctionCall { name, args } => self.compile_function_call(name, args),
            Stmt::BinaryExpression {
                left,
                right,
                operator,
            } => self.compile_binary_expression(*left, *right, operator),
            Stmt::Identifier { name } => self.compile_identifier(name),
            Stmt::Literal { value } => {
                Some(self.context.i32_type().const_int(value as u64, true).into())
            }
            // Import statements are handled at compile time, not runtime
            Stmt::ImportModule { .. }
            | Stmt::ImportModules { .. }
            | Stmt::ImportEverything { .. }
            | Stmt::ImportCollection { .. } => None,
            Stmt::FunctionParam { .. } => {
                // Function parameters are handled in function declaration
                None
            }
        }
    }

    fn compile_declaration(
        &mut self,
        name: String,
        value: Option<Box<Stmt>>,
    ) -> Option<BasicValueEnum<'ctx>> {
        let i32_type = self.context.i32_type();

        // Allocate space for the variable
        let alloca = self.builder.build_alloca(i32_type, &name).unwrap();

        // Store the initial value if provided
        if let Some(val_stmt) = value {
            if let Some(val) = self.compile_stmt(*val_stmt) {
                self.builder.build_store(alloca, val).unwrap();
            } else {
                // Default to 0 if no value provided
                let zero = i32_type.const_int(0, false);
                self.builder.build_store(alloca, zero).unwrap();
            }
        } else {
            // Default to 0 if no value provided
            let zero = i32_type.const_int(0, false);
            self.builder.build_store(alloca, zero).unwrap();
        }

        // Store the variable for later reference
        self.variables.insert(name, alloca);
        None
    }

    fn compile_block(&mut self, body: Vec<Stmt>) -> Option<BasicValueEnum<'ctx>> {
        let mut last_value = None;
        for stmt in body {
            last_value = self.compile_stmt(stmt);
        }
        last_value
    }

    fn compile_if_statement(
        &mut self,
        condition: Stmt,
        block: Stmt,
    ) -> Option<BasicValueEnum<'ctx>> {
        let condition_value = self.compile_stmt(condition)?;

        // Convert condition to boolean (non-zero is true)
        let condition_int = condition_value.into_int_value();
        let zero = self.context.i32_type().const_int(0, false);
        let condition_bool = self
            .builder
            .build_int_compare(IntPredicate::NE, condition_int, zero, "ifcond")
            .unwrap();

        let current_fn = self.current_function?;
        let then_block = self.context.append_basic_block(current_fn, "then");
        let else_block = self.context.append_basic_block(current_fn, "else");
        let merge_block = self.context.append_basic_block(current_fn, "ifcont");

        // Build conditional branch
        self.builder
            .build_conditional_branch(condition_bool, then_block, else_block)
            .unwrap();

        // Build then block
        self.builder.position_at_end(then_block);
        self.compile_stmt(block);
        self.builder
            .build_unconditional_branch(merge_block)
            .unwrap();

        // Build else block (empty for now)
        self.builder.position_at_end(else_block);
        self.builder
            .build_unconditional_branch(merge_block)
            .unwrap();

        // Continue building in merge block
        self.builder.position_at_end(merge_block);
        None
    }

    fn compile_function_declaration(
        &mut self,
        name: String,
        params: Vec<Stmt>,
        return_type: String,
        block: Stmt,
    ) -> Option<BasicValueEnum<'ctx>> {
        // Determine return type
        let ret_type = match return_type.as_str() {
            "int" => self.context.i32_type().into(),
            "void" => self.context.void_type().into(),
            _ => self.context.i32_type().into(), // Default to int
        };

        // Extract parameter types (for now, assume all are int)
        let param_types: Vec<BasicTypeEnum> = params
            .iter()
            .map(|_| self.context.i32_type().into())
            .collect();

        // Create function type
        let fn_type = match return_type.as_str() {
            "void" => self.context.void_type().fn_type(&param_types, false),
            _ => self.context.i32_type().fn_type(&param_types, false),
        };

        // Add function to module
        let function = self.module.add_function(&name, fn_type, None);
        self.functions.insert(name.clone(), function);

        // Create entry block
        let entry_block = self.context.append_basic_block(function, "entry");
        self.builder.position_at_end(entry_block);

        // Save current function context
        let prev_function = self.current_function;
        let prev_variables = self.variables.clone();
        self.current_function = Some(function);

        // Set up parameters
        for (i, param_stmt) in params.iter().enumerate() {
            if let Stmt::FunctionParam {
                name: param_name, ..
            } = param_stmt
            {
                let param_value = function.get_nth_param(i as u32).unwrap();
                let alloca = self
                    .builder
                    .build_alloca(self.context.i32_type(), param_name)
                    .unwrap();
                self.builder.build_store(alloca, param_value).unwrap();
                self.variables.insert(param_name.clone(), alloca);
            }
        }

        // Compile function body
        self.compile_stmt(block);

        // Add return if void function
        if return_type == "void" {
            self.builder.build_return(None).unwrap();
        }

        // Restore previous context
        self.current_function = prev_function;
        self.variables = prev_variables;

        None
    }

    fn compile_function_call(
        &mut self,
        name: String,
        args: Vec<Stmt>,
    ) -> Option<BasicValueEnum<'ctx>> {
        // Get the function
        let function = self.functions.get(&name)?.clone();

        // Compile arguments
        let mut arg_values = Vec::new();
        for arg in args {
            if let Some(val) = self.compile_stmt(arg) {
                arg_values.push(val.into());
            }
        }

        // Call the function
        let call_result = self
            .builder
            .build_call(function, &arg_values, &format!("call_{}", name))
            .unwrap();
        call_result.try_as_basic_value().left()
    }

    fn compile_binary_expression(
        &mut self,
        left: Stmt,
        right: Stmt,
        operator: Operator,
    ) -> Option<BasicValueEnum<'ctx>> {
        let left_val = self.compile_stmt(left)?.into_int_value();
        let right_val = self.compile_stmt(right)?.into_int_value();

        let result = match operator {
            Operator::Add => self
                .builder
                .build_int_add(left_val, right_val, "addtmp")
                .unwrap(),
            Operator::Subtract => self
                .builder
                .build_int_sub(left_val, right_val, "subtmp")
                .unwrap(),
            Operator::Multiply => self
                .builder
                .build_int_mul(left_val, right_val, "multmp")
                .unwrap(),
            Operator::Divide => self
                .builder
                .build_int_signed_div(left_val, right_val, "divtmp")
                .unwrap(),
            Operator::Modulus => self
                .builder
                .build_int_signed_rem(left_val, right_val, "modtmp")
                .unwrap(),
            Operator::GreaterThan => {
                let cmp = self
                    .builder
                    .build_int_compare(IntPredicate::SGT, left_val, right_val, "gttmp")
                    .unwrap();
                self.builder
                    .build_int_z_extend(cmp, self.context.i32_type(), "gtexttmp")
                    .unwrap()
            }
            Operator::GreaterThanEqual => {
                let cmp = self
                    .builder
                    .build_int_compare(IntPredicate::SGE, left_val, right_val, "getmp")
                    .unwrap();
                self.builder
                    .build_int_z_extend(cmp, self.context.i32_type(), "geexttmp")
                    .unwrap()
            }
            Operator::Equal => {
                let cmp = self
                    .builder
                    .build_int_compare(IntPredicate::EQ, left_val, right_val, "eqtmp")
                    .unwrap();
                self.builder
                    .build_int_z_extend(cmp, self.context.i32_type(), "eqexttmp")
                    .unwrap()
            }
            Operator::NotEqual => {
                let cmp = self
                    .builder
                    .build_int_compare(IntPredicate::NE, left_val, right_val, "netmp")
                    .unwrap();
                self.builder
                    .build_int_z_extend(cmp, self.context.i32_type(), "neexttmp")
                    .unwrap()
            }
            Operator::LessThanEqual => {
                let cmp = self
                    .builder
                    .build_int_compare(IntPredicate::SLE, left_val, right_val, "letmp")
                    .unwrap();
                self.builder
                    .build_int_z_extend(cmp, self.context.i32_type(), "leexttmp")
                    .unwrap()
            }
            Operator::LessThan => {
                let cmp = self
                    .builder
                    .build_int_compare(IntPredicate::SLT, left_val, right_val, "lttmp")
                    .unwrap();
                self.builder
                    .build_int_z_extend(cmp, self.context.i32_type(), "ltexttmp")
                    .unwrap()
            }
        };

        Some(result.into())
    }

    fn compile_identifier(&mut self, name: String) -> Option<BasicValueEnum<'ctx>> {
        let var_ptr = self.variables.get(&name)?;
        let loaded_val = self
            .builder
            .build_load(self.context.i32_type(), *var_ptr, &name)
            .unwrap();
        Some(loaded_val)
    }

    pub fn get_ir_string(&self) -> String {
        self.module.print_to_string().to_string()
    }
}
